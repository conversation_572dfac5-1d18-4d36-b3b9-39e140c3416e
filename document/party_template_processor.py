import os
import re
import logging
from docx import Document
from docx.shared import Pt
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls

logger = logging.getLogger(__name__)

class PartyTemplateProcessor:
    """多原告被告模板处理器，用于处理多当事人的情况"""
    
    def __init__(self, templates_dir=None, debug=False):
        """
        初始化多当事人模板处理器
        
        Args:
            templates_dir (str): 模板目录
            debug (bool): 是否开启调试模式
        """
        self.templates_dir = templates_dir
        self.debug = debug
        self.party_templates = {
            '原告（自然人）': '通用-原告（自然人）.docx',
            '原告（法人、非法人组织）': '通用-原告（法人、非法人组织）.docx',
            '被告（自然人）': '通用-被告（自然人）.docx',
            '被告（法人、非法人组织）': '通用-被告（法人、非法人组织）.docx',
            '第三人（自然人）': '通用-第三人（自然人）.docx',
            '第三人（法人、非法人组织）': '通用-第三人（法人、非法人组织）.docx',
        }
    
    def log(self, message):
        """输出日志"""
        if self.debug:
            logger.info(message)
    
    def process_party_templates(self, doc, data):
        """
        处理多当事人模板，替换主文档中的插入点

        Args:
            doc (Document): 主文档对象
            data (dict): 包含当事人信息的数据字典

        Returns:
            Document: 处理后的文档
        """
        self.log("开始处理多当事人模板...")

        # 定义处理的当事人类型 - 按照文档顺序处理，避免插入顺序影响位置
        all_party_types = [
            # 自然人类型
            ('原告（自然人）列表', '原告（自然人）', '原告（自然人）插入点'),
            ('被告（自然人）列表', '被告（自然人）', '被告（自然人）插入点'),
            ('第三人（自然人）列表', '第三人（自然人）', '第三人（自然人）插入点'),
            # 法人类型
            ('原告（法人、非法人组织）列表', '原告（法人、非法人组织）', '原告（法人、非法人组织）插入点'),
            ('被告（法人、非法人组织）列表', '被告（法人、非法人组织）', '被告（法人、非法人组织）插入点'),
            ('第三人（法人、非法人组织）列表', '第三人（法人、非法人组织）', '第三人（法人、非法人组织）插入点')
        ]

        # 首先收集所有插入点信息，按照在文档中的位置排序
        insertion_tasks = []

        for list_key, party_type, marker in all_party_types:
            self.log(f"查找当事人类型: {list_key}, 标记: {marker}")

            # 查找插入点
            insertion_point = self.find_insertion_point(doc, f"{{{{{marker}}}}}")
            if insertion_point is not None:
                placeholder_para, para_index, table_position = insertion_point

                # 检查数据中是否有该类型的当事人列表
                party_list = None
                if list_key in data and isinstance(data[list_key], list) and data[list_key]:
                    party_list = data[list_key]
                    self.log(f"找到当事人数据: {list_key}, 共 {len(party_list)} 条")

                # 记录插入任务，包含位置信息用于排序
                insertion_tasks.append({
                    'list_key': list_key,
                    'party_type': party_type,
                    'marker': marker,
                    'insertion_point': insertion_point,
                    'party_list': party_list,
                    'para_index': para_index if para_index is not None else float('inf'),  # 表格位置排在最后
                    'table_position': table_position
                })
            else:
                self.log(f"未找到插入点标记: {marker}")

        # 按照段落索引从后往前排序，这样插入时不会影响前面段落的索引
        insertion_tasks.sort(key=lambda x: x['para_index'], reverse=True)

        # 按顺序执行插入任务
        for task in insertion_tasks:
            self.log(f"执行插入任务: {task['list_key']}")

            if task['party_list']:
                # 有当事人数据，执行插入
                self.insert_party_sections(
                    doc,
                    self.party_templates[task['party_type']],
                    task['party_list'],
                    task['insertion_point'],
                    task['party_type']
                )
            else:
                # 没有当事人数据，仅清空插入点
                self.log(f"未找到 {task['list_key']} 数据或数据为空，仅清除插入点标记")
                placeholder_para, para_index, table_position = task['insertion_point']
                placeholder_para.text = ""

        # 最后再扫描一次文档，确保所有插入点都被清理
        self._ensure_all_markers_removed(doc)

        return doc
    
    def find_insertion_point(self, doc, marker):
        """
        在文档中查找插入点标记
        
        Args:
            doc (Document): Word文档对象
            marker (str): 插入点标记文本
            
        Returns:
            tuple: 包含段落对象和位置信息的元组
        """
        # 在段落中查找
        for i, para in enumerate(doc.paragraphs):
            if marker in para.text:
                self.log(f"在段落 {i} 中找到标记: {marker}")
                return (para, i, None)
        
        # 在表格中查找
        for t, table in enumerate(doc.tables):
            for r, row in enumerate(table.rows):
                for c, cell in enumerate(row.cells):
                    for p, para in enumerate(cell.paragraphs):
                        if marker in para.text:
                            self.log(f"在表格 {t}, 行 {r}, 单元格 {c}, 段落 {p} 中找到标记: {marker}")
                            return (para, None, (t, r, c, p))
        
        return None
    
    def insert_party_sections(self, main_doc, template_filename, party_list,
                             insertion_point, party_type):
        """
        将当事人模板插入到主文档中

        Args:
            main_doc (Document): 主文档对象
            template_filename (str): 当事人模板文件名
            party_list (list): 当事人数据列表
            insertion_point (tuple): 插入点信息
            party_type (str): 当事人类型名称（用于变量替换）
        """
        if not insertion_point:
            self.log("无效的插入点，无法插入当事人部分")
            return

        # 解包插入点信息
        placeholder_para, para_index, table_position = insertion_point

        # 构建模板完整路径
        template_path = os.path.join(self.templates_dir, template_filename)
        if not os.path.exists(template_path):
            self.log(f"当事人模板文件不存在: {template_path}")
            return

        self.log(f"使用模板: {template_path}")
        self.log(f"将为 {len(party_list)} 个 {party_type} 插入内容到插入点")

        # 移除插入点标记段落内容
        placeholder_para.text = ""

        # 记录当前插入位置的段落，用于后续插入
        current_insert_para = placeholder_para

        # 为每个当事人生成内容
        for i, party_data in enumerate(party_list):
            self.log(f"处理第 {i+1}/{len(party_list)} 个 {party_type}: {party_data.get('姓名', '未知')}")

            try:
                # 加载当事人模板
                party_doc = Document(template_path)

                # 替换模板中的变量，传入当事人索引
                for para in party_doc.paragraphs:
                    self._replace_party_placeholders(para, party_data, party_type, i+1)

                # 处理模板中的表格
                for table in party_doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for para in cell.paragraphs:
                                self._replace_party_placeholders(para, party_data, party_type, i+1)

                # 插入处理后的内容到主文档
                # 每次插入后，更新插入位置为最后插入的段落
                last_inserted_para = self._insert_document_content_improved(
                    main_doc, party_doc, current_insert_para, party_type, i+1
                )

                # 更新下一次插入的位置
                if last_inserted_para:
                    current_insert_para = last_inserted_para

            except Exception as e:
                self.log(f"处理当事人模板时出错: {str(e)}")

        # 如果是表格中的段落，可能需要额外处理
        if table_position:
            self.log(f"清理表格中的插入点标记")
    
    def _replace_party_placeholders(self, paragraph, party_data, party_type, party_index=1):
        """
        替换当事人模板中的占位符，转换为带索引的新占位符以便表单生成

        Args:
            paragraph (Paragraph): 段落对象
            party_data (dict): 当事人数据
            party_type (str): 当事人类型名称
            party_index (int): 当事人索引
        """
        if '{{' not in paragraph.text or '}}' not in paragraph.text:
            return

        self.log(f"处理当事人占位符前: {paragraph.text}")

        # 保存原始文本用于比较
        original_text = paragraph.text
        text = original_text

        # 检查段落是否包含可替换的占位符
        contains_placeholder = False

        # 替换当事人模板中的占位符为带索引的新占位符
        pattern = r'{{([^}]+)}}'
        matches = list(re.finditer(pattern, text))

        for match in matches:
            contains_placeholder = True
            full_match = match.group(0)  # 如 {{姓名}}
            attr_name = match.group(1)   # 如 姓名

            # 检查是否是当事人列表类型相关的占位符，这些不需要处理
            if attr_name.startswith(party_type) or attr_name in [
                '原告（自然人）列表', '原告（法人、非法人组织）列表',
                '被告（自然人）列表', '被告（法人、非法人组织）列表',
                '第三人（自然人）列表', '第三人（法人、非法人组织）列表'
            ]:
                continue

            # 跳过插入点标记
            if '插入点' in attr_name:
                continue

            # 生成带索引的新占位符名称
            # 例如：{{姓名}} -> {{原告_1_姓名}}
            party_type_short = party_type.replace('（', '_').replace('）', '_').replace('、', '_')
            new_placeholder_name = f"{party_type_short}_{party_index}_{attr_name}"
            new_placeholder = f"{{{{{new_placeholder_name}}}}}"

            # 替换占位符
            text = text.replace(full_match, new_placeholder)

            self.log(f"占位符转换: {full_match} -> {new_placeholder}")

        # 如果文本有变化，更新段落
        if text != original_text:
            paragraph.text = text
            self.log(f"处理当事人占位符后: {paragraph.text}")
    
    def _insert_document_content(self, target_doc, source_doc, target_paragraph):
        """
        将源文档内容插入到目标文档的指定段落位置
        
        Args:
            target_doc (Document): 目标文档
            source_doc (Document): 源文档
            target_paragraph (Paragraph): 目标段落
        """
        # 添加额外的调试信息
        self.log(f"开始插入源文档内容，源文档段落数: {len(source_doc.paragraphs)}, 表格数: {len(source_doc.tables)}")
        
        # 改进的空文档检测逻辑
        has_content = False
        
        # 检查段落内容
        for para in source_doc.paragraphs:
            if para.text.strip():
                has_content = True
                self.log(f"模板中存在非空段落: {para.text[:50]}...")
                break
                
        # 检查表格内容
        if not has_content and source_doc.tables:
            for table in source_doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        cell_text = ''.join(p.text for p in cell.paragraphs)
                        if cell_text.strip():
                            has_content = True
                            self.log(f"模板中存在非空表格单元格: {cell_text[:50]}...")
                            break
                    if has_content:
                        break
                if has_content:
                    break
                    
        # 检查当事人模板是否为空
        if not has_content:
            self.log("警告: 当事人模板文档为空或只包含空白内容!")
            # 添加一些临时文本表明此处应有当事人信息
            new_run = target_paragraph.add_run("")
            new_run.bold = True
            return
        
        # 获取目标段落的父级元素
        parent_element = target_paragraph._p.getparent()
        if parent_element is None:
            self.log("错误: 无法获取目标段落的父级元素!")
            return
            
        # 获取目标段落的索引
        try:
            index = list(parent_element).index(target_paragraph._p)
            self.log(f"找到目标段落的索引: {index}")
        except ValueError:
            self.log("错误: 无法获取目标段落在父级元素中的索引!")
            return
        
        # 记录要插入的所有元素
        elements_to_insert = []
        
        # 获取源文档中的所有元素并移除换行符
        try:
            # 处理源文档中的所有元素
            for element in source_doc._body._body:
                # 深度复制元素，保持所有格式、字体和样式属性
                import copy
                new_element = copy.deepcopy(element)
                
                # 如果是段落且只包含换行符，则跳过不添加
                if new_element.tag.endswith('p'):
                    # 检查段落是否只包含空内容或换行符
                    is_empty = True
                    for child in new_element:
                        if child.tag.endswith('r'):  # 如果是文本运行
                            for text_element in child:
                                if text_element.tag.endswith('t') and text_element.text and text_element.text.strip():
                                    is_empty = False
                                    break
                        if not is_empty:
                            break
                            
                    if is_empty:
                        self.log("跳过空段落或只包含换行符的段落")
                        continue
                
                elements_to_insert.append(new_element)
            
            self.log(f"准备插入 {len(elements_to_insert)} 个元素")
        except Exception as e:
            self.log(f"复制源文档元素时出错: {str(e)}")
            return
        
        # 将所有元素插入到目标文档
        try:
            for element in elements_to_insert:
                index += 1
                parent_element.insert(index, element)
            
            self.log(f"已成功插入 {len(elements_to_insert)} 个元素")
        except Exception as e:
            self.log(f"插入元素到目标文档时出错: {str(e)}")
            return
            
        self.log("已完整插入通用模板内容，包括所有表格和格式信息")

    def _insert_document_content_improved(self, target_doc, source_doc, target_paragraph, party_type, party_index):
        """
        改进的文档内容插入方法，返回最后插入的段落

        Args:
            target_doc (Document): 目标文档
            source_doc (Document): 源文档
            target_paragraph (Paragraph): 目标段落
            party_type (str): 当事人类型
            party_index (int): 当事人序号

        Returns:
            Paragraph: 最后插入的段落，用于下次插入的位置参考
        """
        self.log(f"开始插入第 {party_index} 个 {party_type} 的内容")

        # 检查源文档是否有内容
        has_content = False
        for para in source_doc.paragraphs:
            if para.text.strip():
                has_content = True
                break

        if not has_content and source_doc.tables:
            for table in source_doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        cell_text = ''.join(p.text for p in cell.paragraphs)
                        if cell_text.strip():
                            has_content = True
                            break
                    if has_content:
                        break
                if has_content:
                    break

        if not has_content:
            self.log(f"警告: {party_type} 模板文档为空")
            return target_paragraph

        # 获取目标段落的父级元素
        parent_element = target_paragraph._p.getparent()
        if parent_element is None:
            self.log("错误: 无法获取目标段落的父级元素!")
            return target_paragraph

        # 获取目标段落的索引
        try:
            index = list(parent_element).index(target_paragraph._p)
            self.log(f"找到目标段落的索引: {index}")
        except ValueError:
            self.log("错误: 无法获取目标段落在父级元素中的索引!")
            return target_paragraph

        # 收集要插入的元素
        elements_to_insert = []
        last_paragraph = None

        try:
            # 处理源文档中的所有元素
            for element in source_doc._body._body:
                import copy
                new_element = copy.deepcopy(element)

                # 跳过空段落
                if new_element.tag.endswith('p'):
                    is_empty = True
                    for child in new_element:
                        if child.tag.endswith('r'):
                            for text_element in child:
                                if text_element.tag.endswith('t') and text_element.text and text_element.text.strip():
                                    is_empty = False
                                    break
                        if not is_empty:
                            break

                    if is_empty:
                        continue

                elements_to_insert.append(new_element)

            self.log(f"准备插入 {len(elements_to_insert)} 个元素")
        except Exception as e:
            self.log(f"复制源文档元素时出错: {str(e)}")
            return target_paragraph

        # 插入所有元素
        try:
            for element in elements_to_insert:
                index += 1
                parent_element.insert(index, element)

                # 如果是段落，记录为最后插入的段落
                if element.tag.endswith('p'):
                    # 找到对应的段落对象
                    for para in target_doc.paragraphs:
                        if para._element == element:
                            last_paragraph = para
                            break

            self.log(f"已成功插入 {len(elements_to_insert)} 个元素")

            # 返回最后插入的段落，如果没有找到则返回原段落
            return last_paragraph if last_paragraph else target_paragraph

        except Exception as e:
            self.log(f"插入元素到目标文档时出错: {str(e)}")
            return target_paragraph

    def _ensure_all_markers_removed(self, doc):
        """
        确保所有插入点标记都被清理
        
        Args:
            doc (Document): Word文档对象
        """
        self.log("最终检查，确保所有插入点标记都已被清理...")
        
        # 定义所有可能的插入点标记
        markers = [
            '{{原告（自然人）插入点}}',
            '{{原告（法人、非法人组织）插入点}}',
            '{{被告（自然人）插入点}}',
            '{{被告（法人、非法人组织）插入点}}',
            '{{第三人（自然人）插入点}}',
            '{{第三人（法人、非法人组织）插入点}}'
        ]
        
        # 检查段落中的插入点标记
        for i, para in enumerate(doc.paragraphs):
            for marker in markers:
                if marker in para.text:
                    self.log(f"清理漏网插入点 - 段落 {i}: {marker}")
                    para.text = para.text.replace(marker, "")
        
        # 检查表格中的插入点标记
        for t, table in enumerate(doc.tables):
            for r, row in enumerate(table.rows):
                for c, cell in enumerate(row.cells):
                    for p, para in enumerate(cell.paragraphs):
                        for marker in markers:
                            if marker in para.text:
                                self.log(f"清理漏网插入点 - 表格 {t}, 行 {r}, 单元格 {c}, 段落 {p}: {marker}")
                                para.text = para.text.replace(marker, "") 