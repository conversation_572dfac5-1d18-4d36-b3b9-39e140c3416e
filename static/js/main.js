document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const fileInfo = document.getElementById('file-info');
    const uploadForm = document.getElementById('upload-form');
    const submitBtn = document.getElementById('submit-btn');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const statusStage = document.getElementById('status-stage');
    const classificationMode = document.getElementById('classification-mode');
    const manualTemplateContainer = document.getElementById('manual-template-container');
    const templateSelect = document.getElementById('template-select');
    const fileListContainer = document.getElementById('file-list-container');
    const fileList = document.getElementById('file-list');
    
    // 模板下载相关DOM元素
    const templateDownloadSelect = document.getElementById('template-download-select');
    const downloadTemplateBtn = document.getElementById('download-template-btn');
    
    // 文件列表
    let selectedFiles = [];
    
    // 初始化排序功能
    let sortable = null;
    
    // 加载模板下载列表
    function loadTemplates2() {
        fetch('/api/templates2')
            .then(response => response.json())
            .then(data => {
                // 清空当前选项，保留默认选项
                templateDownloadSelect.innerHTML = '<option value="" disabled selected>请选择模板</option>';
                
                // 添加模板选项
                if (data.templates && data.templates.length > 0) {
                    data.templates.forEach(template => {
                        const option = document.createElement('option');
                        option.value = template.filename;
                        option.textContent = template.name;
                        templateDownloadSelect.appendChild(option);
                    });
                    
                    // 启用下拉框
                    templateDownloadSelect.disabled = false;
                } else {
                    // 如果没有模板，添加一个提示选项
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = '没有可用的模板';
                    option.disabled = true;
                    templateDownloadSelect.appendChild(option);
                    
                    // 禁用下拉框
                    templateDownloadSelect.disabled = true;
                }
            })
            .catch(error => {
                console.error('加载模板列表失败:', error);
                // 出错时添加一个提示选项
                templateDownloadSelect.innerHTML = '<option value="" disabled selected>加载失败</option>';
                templateDownloadSelect.disabled = true;
            });
    }
    
    // 加载案件类型列表（原有功能）
    function loadCaseTypes() {
        fetch('/api/templates')
            .then(response => response.json())
            .then(data => {
                // 清空当前选项
                templateSelect.innerHTML = '';
                
                // 添加模板选项
                if (data.templates && data.templates.length > 0) {
                    data.templates.forEach(template => {
                        const option = document.createElement('option');
                        option.value = template.filename;
                        option.textContent = template.name;
                        templateSelect.appendChild(option);
                    });
                } else {
                    // 如果没有模板，添加一个提示选项
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = '没有可用的模板';
                    option.disabled = true;
                    option.selected = true;
                    templateSelect.appendChild(option);
                }
            })
            .catch(error => {
                console.error('加载模板列表失败:', error);
                // 出错时添加一个提示选项
                templateSelect.innerHTML = '';
                const option = document.createElement('option');
                option.value = '';
                option.textContent = '加载模板失败';
                option.disabled = true;
                option.selected = true;
                templateSelect.appendChild(option);
            });
    }
    
    // 监听模板下载选择框变化
    if (templateDownloadSelect) {
        templateDownloadSelect.addEventListener('change', function() {
            if (this.value) {
                downloadTemplateBtn.disabled = false;
            } else {
                downloadTemplateBtn.disabled = true;
            }
        });
    }
    
    // 监听下载模板按钮点击事件
    if (downloadTemplateBtn) {
        downloadTemplateBtn.addEventListener('click', function() {
            const selectedTemplate = templateDownloadSelect.value;
            if (selectedTemplate) {
                // 创建下载链接
                window.location.href = `/download-template/${selectedTemplate}`;
            }
        });
    }
    
    // 监听分类模式变化
    classificationMode.addEventListener('change', function() {
        if (this.value === 'manual') {
            manualTemplateContainer.style.display = 'block';
        } else {
            manualTemplateContainer.style.display = 'none';
        }
    });
    
    // 页面加载时请求案件类型和模板下载列表
    loadCaseTypes();
    loadTemplates2();
    
    // 阻止默认拖放行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // 高亮拖放区域
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropArea.classList.add('highlight');
    }
    
    function unhighlight() {
        dropArea.classList.remove('highlight');
    }
    
    // 处理拖放文件
    dropArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const droppedFiles = dt.files;
        
        if (droppedFiles.length > 0) {
            // 创建一个新的 FileList 对象
            const dataTransfer = new DataTransfer();
            
            // 如果已经有选择的文件，先添加它们
            if (fileInput.files.length > 0) {
                Array.from(fileInput.files).forEach(file => {
                    dataTransfer.items.add(file);
                });
            }
            
            // 添加新拖放的文件
            Array.from(droppedFiles).forEach(file => {
                dataTransfer.items.add(file);
            });
            
            // 更新文件输入
            fileInput.files = dataTransfer.files;
            updateFilesInfo();
        }
    }
    
    // 处理文件选择
    fileInput.addEventListener('change', function() {
        updateFilesInfo();
    });
    
    // 更新文件信息和列表
    function updateFilesInfo() {
        if (fileInput.files.length > 0) {
            // 更新文件信息文本
            fileInfo.textContent = `已选择 ${fileInput.files.length} 个文件`;
            
            // 更新文件列表
            selectedFiles = Array.from(fileInput.files);
            renderFileList();
            
            // 显示文件列表容器
            fileListContainer.style.display = 'block';
            
            // 启用提交按钮
            submitBtn.disabled = false;
        } else {
            fileInfo.textContent = '支持的格式：JPG, PNG, PDF, DOC, DOCX';
            fileListContainer.style.display = 'none';
            submitBtn.disabled = true;
        }
    }
    
    // 渲染文件列表
    function renderFileList() {
        // 清空文件列表
        fileList.innerHTML = '';
        
        // 添加文件到列表
        selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.index = index;
            
            // 文件类型图标
            let iconClass = 'fa-file';
            if (file.type.includes('image')) {
                iconClass = 'fa-file-image';
            } else if (file.type.includes('pdf')) {
                iconClass = 'fa-file-pdf';
            } else if (file.type.includes('word')) {
                iconClass = 'fa-file-word';
            }
            
            fileItem.innerHTML = `
                <div class="file-item-handle">
                    <i class="fas fa-grip-vertical"></i>
                </div>
                <i class="fas ${iconClass} file-item-icon"></i>
                <span class="file-item-name">${file.name}</span>
                <div class="file-item-actions">
                    <button type="button" class="remove-file" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            fileList.appendChild(fileItem);
        });
        
        // 初始化排序功能
        if (sortable) {
            sortable.destroy();
        }
        
        sortable = new Sortable(fileList, {
            animation: 150,
            handle: '.file-item-handle',
            ghostClass: 'sortable-ghost',
            onEnd: handleSortEnd
        });
        
        // 为删除按钮添加事件监听
        document.querySelectorAll('.remove-file').forEach(button => {
            button.addEventListener('click', handleRemoveFile);
        });
    }
    
    // 处理文件排序结束事件
    function handleSortEnd(evt) {
        // 获取旧位置和新位置
        const oldIndex = evt.oldIndex;
        const newIndex = evt.newIndex;
        
        // 如果位置没有变化，不需要处理
        if (oldIndex === newIndex) return;
        
        // 更新selectedFiles数组中的文件顺序
        const movedFile = selectedFiles.splice(oldIndex, 1)[0];
        selectedFiles.splice(newIndex, 0, movedFile);
        
        // 更新文件输入字段的文件列表
        updateFileInputFiles();
    }
    
    // 处理删除文件
    function handleRemoveFile(e) {
        const index = parseInt(e.currentTarget.dataset.index);
        
        // 从selectedFiles中删除文件
        selectedFiles.splice(index, 1);
        
        // 更新文件输入和界面
        updateFileInputFiles();
    }
    
    // 更新文件输入字段的文件列表
    function updateFileInputFiles() {
        if (selectedFiles.length === 0) {
            fileInput.value = ''; // 清空文件输入
            fileListContainer.style.display = 'none';
            fileInfo.textContent = '支持的格式：JPG, PNG, PDF, DOC, DOCX';
            submitBtn.disabled = true;
            return;
        }
        
        // 创建一个新的DataTransfer对象
        const dataTransfer = new DataTransfer();
        
        // 添加所有选中的文件
        selectedFiles.forEach(file => {
            dataTransfer.items.add(file);
        });
        
        // 将新的FileList赋值给文件输入
        fileInput.files = dataTransfer.files;
        
        // 更新文件信息和列表
        fileInfo.textContent = `已选择 ${selectedFiles.length} 个文件`;
        renderFileList();
    }
    
    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes < 1024) {
            return bytes + ' B';
        } else if (bytes < 1024 * 1024) {
            return (bytes / 1024).toFixed(2) + ' KB';
        } else {
            return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
        }
    }
    
    // 处理表单提交
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (fileInput.files.length === 0) {
            alert('请选择文件！');
            return;
        }
        
        // 显示进度条
        progressContainer.style.display = 'block';
        submitBtn.disabled = true;
        
        // 创建FormData对象
        const formData = new FormData(uploadForm);
        
        // 确保在手动模式下模板选择被包含
        if (classificationMode.value === 'manual') {
            if (!formData.has('template_type') || formData.get('template_type') === '') {
                // 如果没有选择模板，使用第一个可用的模板
                if (templateSelect.options.length > 0) {
                    formData.set('template_type', templateSelect.options[0].value);
                }
            }
        }
        
        // 发送AJAX请求
        const xhr = new XMLHttpRequest();
        xhr.open('POST', uploadForm.action, true);
        
        // 监听上传进度
        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressText.textContent = `上传中...${Math.round(percentComplete)}%`;
                
                // 当上传完成时显示处理中状态
                if (percentComplete >= 100) {
                    progressText.textContent = '上传完成，正在处理...';
                    statusStage.textContent = '上传完成';
                }
            }
        });
        
        // 上传完成事件
        xhr.upload.addEventListener('load', function() {
            console.log('上传完成，等待服务器处理...');
            progressText.textContent = '上传完成，正在处理...';
            statusStage.textContent = '上传完成';
            progressBar.style.width = '40%';
            progressBar.style.backgroundColor = '#3498db';
        });
        
        // 处理请求完成
        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                console.log('服务器响应成功，开始状态查询循环');
                progressText.textContent = '处理中...请稍候';
                
                // 定期查询处理状态
                let lastStatus = '';
                let errorCount = 0;
                const statusCheckInterval = setInterval(function() {
                    console.log('查询状态...');
                    fetch('/status', { 
                        method: 'GET',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-Timestamp': new Date().getTime() // 添加时间戳防止缓存
                        },
                        credentials: 'same-origin',
                        cache: 'no-store' // 禁用缓存
                    })
                        .then(response => {
                            console.log('状态响应:', response);
                            if (!response.ok) {
                                throw new Error(`状态查询错误: ${response.status} ${response.statusText}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('收到状态数据:', data);
                            errorCount = 0; // 重置错误计数
                            
                            // 检查状态是否发生变化
                            if (data.status !== lastStatus) {
                                console.log(`状态变化: ${lastStatus} -> ${data.status}`);
                                lastStatus = data.status;
                                
                                // 状态变化时添加明显的视觉反馈
                                statusStage.classList.add('status-changed');
                                setTimeout(() => {
                                    statusStage.classList.remove('status-changed');
                                }, 1000);
                            }
                            
                            // 更新状态显示
                            progressText.textContent = `${data.message}`;
                            statusStage.textContent = data.status;
                            
                            // 根据状态设置进度条
                            switch(data.status) {
                                case '开始':
                                    progressBar.style.width = '10%';
                                    break;
                                case '上传':
                                    progressBar.style.width = '30%';
                                    break;
                                case 'OCR':
                                case 'OCR中':
                                    progressBar.style.width = '50%';
                                    break;
                                case '等待确认':
                                    // OCR完成，等待用户确认
                                    progressBar.style.width = '60%';
                                    clearInterval(statusCheckInterval);

                                    console.log('检测到等待确认状态，显示确认弹窗');

                                    // 显示确认弹窗
                                    if (data.ocr_text && data.task_id) {
                                        showConfirmationModal(data.ocr_text, data.task_id, statusCheckInterval);
                                    } else {
                                        console.error('缺少OCR文本或任务ID');
                                        alert('无法显示确认弹窗，请刷新页面重试');
                                    }
                                    break;
                                case 'LLM':
                                case 'LLM处理':
                                    progressBar.style.width = '70%';
                                    break;
                                case '生成文档':
                                    progressBar.style.width = '90%';
                                    break;
                                case '文档编辑':
                                    // 文档编辑状态 - 自动跳转到编辑页面
                                    progressBar.style.width = '85%';
                                    clearInterval(statusCheckInterval);

                                    console.log('检测到文档编辑状态，准备跳转到编辑页面');

                                    // 检查是否有编辑URL
                                    if (data.edit_url) {
                                        console.log('使用状态中的edit_url跳转:', data.edit_url);
                                        setTimeout(() => {
                                            window.location.href = data.edit_url;
                                        }, 500); // 短暂延迟让用户看到状态变化
                                    } else {
                                        console.log('未找到edit_url，尝试从初始响应获取');
                                        // 尝试从初始响应中获取编辑URL
                                        try {
                                            const response = JSON.parse(xhr.responseText);
                                            if (response.edit_url) {
                                                console.log('使用初始响应中的edit_url:', response.edit_url);
                                                setTimeout(() => {
                                                    window.location.href = response.edit_url;
                                                }, 500);
                                                return;
                                            }
                                        } catch (error) {
                                            console.log('解析初始响应失败:', error);
                                        }

                                        // 如果都没有找到，显示错误
                                        console.error('未找到编辑页面URL');
                                        alert('无法跳转到编辑页面，请刷新页面重试');
                                    }
                                    break;
                                case '完成':
                                    progressBar.style.width = '100%';
                                    clearInterval(statusCheckInterval);

                                    // 处理完成后跳转到结果页面
                                    console.log('处理完成，准备跳转到结果页面');

                                    // 等待短暂再跳转，让用户看到完成状态
                                    setTimeout(() => {
                                        // 先尝试使用初始响应中的redirect
                                        try {
                                            const response = JSON.parse(xhr.responseText);
                                            if (response.redirect) {
                                                console.log('使用初始响应中的redirect:', response.redirect);
                                                window.location.href = response.redirect;
                                                return;
                                            }
                                        } catch (error) {
                                            console.log('解析初始响应失败:', error);
                                        }

                                        // 如果没有redirect或解析失败，直接跳转到结果页面
                                        console.log('直接跳转到结果页面');
                                        window.location.href = '/result';
                                    }, 1000);  // 增加等待时间到1秒，确保用户能看到完成状态
                                    break;
                                case 'template_selection_required':
                                    // 模板选择状态 - LLM找不到合适模板，请用户手动选择
                                    progressBar.style.width = '80%';
                                    clearInterval(statusCheckInterval);
                                    
                                    // 创建模板选择对话框
                                    const dialog = document.createElement('div');
                                    dialog.className = 'template-selection-dialog';
                                    dialog.innerHTML = `
                                        <div class="dialog-content">
                                            <h3>模板选择</h3>
                                            <p>自动模板选择未找到合适的模板，请手动选择一个模板继续。</p>
                                            <select id="manual-template-select" class="template-select">
                                                ${data.available_templates.map(template => 
                                                    `<option value="${template}">${template}</option>`
                                                ).join('')}
                                            </select>
                                            <div class="dialog-buttons">
                                                <button id="confirm-template-btn" class="dialog-btn confirm">确认</button>
                                                <button id="cancel-template-btn" class="dialog-btn cancel">取消</button>
                                            </div>
                                        </div>
                                    `;
                                    
                                    // 添加样式
                                    const style = document.createElement('style');
                                    style.textContent = `
                                        .template-selection-dialog {
                                            position: fixed;
                                            top: 0;
                                            left: 0;
                                            width: 100%;
                                            height: 100%;
                                            background-color: rgba(0, 0, 0, 0.5);
                                            display: flex;
                                            justify-content: center;
                                            align-items: center;
                                            z-index: 1000;
                                        }
                                        .dialog-content {
                                            background-color: white;
                                            border-radius: 8px;
                                            padding: 20px;
                                            width: 80%;
                                            max-width: 500px;
                                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                                        }
                                        .template-select {
                                            width: 100%;
                                            padding: 10px;
                                            margin: 15px 0;
                                            border-radius: 4px;
                                            border: 1px solid #ddd;
                                        }
                                        .dialog-buttons {
                                            display: flex;
                                            justify-content: flex-end;
                                            gap: 10px;
                                            margin-top: 15px;
                                        }
                                        .dialog-btn {
                                            padding: 8px 15px;
                                            border-radius: 4px;
                                            border: none;
                                            cursor: pointer;
                                        }
                                        .confirm {
                                            background-color: #3498db;
                                            color: white;
                                        }
                                        .cancel {
                                            background-color: #e74c3c;
                                            color: white;
                                        }
                                    `;
                                    document.head.appendChild(style);
                                    document.body.appendChild(dialog);
                                    
                                    // 添加事件处理
                                    document.getElementById('confirm-template-btn').addEventListener('click', function() {
                                        const selectedTemplate = document.getElementById('manual-template-select').value;
                                        
                                        // 准备请求数据
                                        const templateSelectData = new FormData();
                                        templateSelectData.append('template_type', selectedTemplate);
                                        templateSelectData.append('classification_mode', 'manual');
                                        
                                        // 如果服务器保存了格式化文本，也一并发送
                                        if(data.formatted_text) {
                                            templateSelectData.append('formatted_text_json', JSON.stringify(data.formatted_text));
                                        }
                                        
                                        // 发送请求继续处理
                                        fetch('/continue_with_template', {
                                            method: 'POST',
                                            body: templateSelectData
                                        })
                                        .then(response => response.json())
                                        .then(responseData => {
                                            // 移除对话框
                                            document.body.removeChild(dialog);
                                            
                                            // 处理响应，一般是跳转到结果页面
                                            if (responseData.redirect) {
                                                window.location.href = responseData.redirect;
                                            } else {
                                                // 恢复状态查询
                                                lastStatus = '';
                                                progressBar.style.width = '70%';
                                                progressText.textContent = '继续处理中...';
                                                const newInterval = setInterval(statusCheck, 1000);
                                                statusCheckInterval = newInterval;
                                            }
                                        })
                                        .catch(error => {
                                            console.error('发送模板选择失败:', error);
                                            alert('模板选择失败，请重试');
                                        });
                                    });
                                    
                                    document.getElementById('cancel-template-btn').addEventListener('click', function() {
                                        // 移除对话框
                                        document.body.removeChild(dialog);
                                        // 重置界面
                                        progressContainer.style.display = 'none';
                                        submitBtn.disabled = false;
                                    });
                                    
                                    break;
                                case '错误':
                                    progressBar.style.width = '100%';
                                    progressBar.style.backgroundColor = '#e74c3c';
                                    clearInterval(statusCheckInterval);
                                    alert('处理出错: ' + data.message);
                                    submitBtn.disabled = false;
                                    break;
                            }
                        })
                        .catch(error => {
                            console.error('状态查询错误:', error);
                            errorCount++;
                            
                            // 连续多次错误才显示错误信息
                            if (errorCount > 3) {
                                // 显示错误信息
                                progressText.textContent = `状态查询失败: ${error.message}`;
                                progressBar.classList.add('error');
                            }
                            
                            // 尝试重新查询，不中断状态查询循环
                            console.log('将在下一次循环重试...');
                        });
                }, 1000); // 每1000毫秒查询一次状态，提高响应速度
            } else {
                progressText.textContent = '上传失败！';
                alert('上传失败：' + xhr.statusText);
                submitBtn.disabled = false;
            }
        });
        
        // 处理错误
        xhr.addEventListener('error', function() {
            progressText.textContent = '上传失败！';
            alert('上传失败：网络错误');
            submitBtn.disabled = false;
        });
        
        // 发送请求
        xhr.send(formData);
    });

    // 扫码上传功能
    // 获取扫码相关DOM元素
    const qrUploadBtn = document.getElementById('qr-upload-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeBtn = document.querySelector('.close-btn');
    const sessionIdEl = document.getElementById('session-id');
    const connectionStatus = document.getElementById('connection-status');
    const qrCodeImage = document.getElementById('qr-code-image');
    const mobileFilesContainer = document.getElementById('mobile-files-container');
    const mobileFilesList = document.getElementById('mobile-files-list');
    
    // 存储移动端上传的文件
    let mobileUploadedFiles = [];
    
    // 打开扫码上传对话框
    if (qrUploadBtn) {
        qrUploadBtn.addEventListener('click', function() {
            // 获取二维码URL
            fetch('/generate-qr-code')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示二维码
                        qrCodeImage.src = data.qr_code_url;
                        
                        // 更新会话ID
                        if (sessionIdEl && data.session_id) {
                            sessionIdEl.textContent = data.session_id;
                        }
                        
                        // 显示对话框
                        qrModal.style.display = 'block';
                        
                        // 建立Socket.IO连接
                        initSocketConnection(data.session_id);
                    } else {
                        alert('生成二维码失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取二维码失败:', error);
                    alert('获取二维码失败，请重试');
                });
        });
    }
    
    // 关闭对话框
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            qrModal.style.display = 'none';
            
            // 如果有上传的文件，添加到文件列表
            if (mobileUploadedFiles.length > 0) {
                addMobileFilesToUploadList(mobileUploadedFiles);
            }
        });
    }
    
    // 点击对话框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === qrModal) {
            qrModal.style.display = 'none';
            
            // 如果有上传的文件，添加到文件列表
            if (mobileUploadedFiles.length > 0) {
                addMobileFilesToUploadList(mobileUploadedFiles);
            }
        }
    });
    
    // 初始化Socket.IO连接
    function initSocketConnection(sessionId) {
        // 创建Socket连接
        const socket = io();
        
        // 更新连接状态
        connectionStatus.textContent = '正在连接...';
        
        // 连接成功
        socket.on('connect', function() {
            connectionStatus.textContent = '已连接';
            
            // 加入会话
            socket.emit('join_session', { session_id: sessionId, client_type: 'pc' });
            
            // 监听移动端加入
            socket.on('mobile_joined', function() {
                connectionStatus.textContent = '手机已连接';
            });
            
            // 监听移动端上传文件
            socket.on('file_uploaded', function(data) {
                // 显示接收到的文件
                const fileData = data.file_data;
                
                // 存储文件数据
                mobileUploadedFiles.push({
                    name: fileData.filename,
                    type: fileData.file_type,
                    size: fileData.file_size,
                    dataUrl: fileData.data
                });
                
                // 更新文件列表
                updateMobileFilesList();
                
                // 更新进度信息
                if (fileData.total_files > 1) {
                    connectionStatus.textContent = `已连接 - 接收文件 ${fileData.current_file}/${fileData.total_files}`;
                }
                
                // 显示文件列表容器
                mobileFilesContainer.style.display = 'block';
                
                // 更新模态框标题 - 添加文件计数
                updateModalHeader();
                
                // 发送上传成功响应
                socket.emit('upload_response', {
                    session_id: sessionId,
                    success: true,
                    message: '文件已成功上传到PC端'
                });
            });
        });
        
        // 连接错误
        socket.on('connect_error', function() {
            connectionStatus.textContent = '连接失败';
        });
        
        // 断开连接
        socket.on('disconnect', function() {
            connectionStatus.textContent = '已断开连接';
        });
    }
    
    // 更新移动端上传的文件列表
    function updateMobileFilesList() {
        // 清空列表
        mobileFilesList.innerHTML = '';
        
        // 添加文件到列表
        mobileUploadedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'mobile-file-item';
            
            // 文件类型图标
            let iconClass = 'fa-file';
            if (file.type.includes('image')) {
                iconClass = 'fa-file-image';
            }
            
            fileItem.innerHTML = `
                <i class="fas ${iconClass} mobile-file-icon"></i>
                <div class="mobile-file-info">
                    <div class="mobile-file-name">${file.name}</div>
                    <div class="mobile-file-size">${formatFileSize(file.size)}</div>
                </div>
            `;
            
            mobileFilesList.appendChild(fileItem);
        });
        
        // 更新标题
        updateModalHeader();
    }
    
    // 更新模态框标题
    function updateModalHeader() {
        // 查找模态框标题
        const modalHeader = document.querySelector('.modal-header h3');
        if (modalHeader) {
            // 移除旧的计数器
            const oldCounter = document.querySelector('.files-counter');
            if (oldCounter) {
                oldCounter.remove();
            }
            
            // 只有在有文件时添加计数器
            if (mobileUploadedFiles.length > 0) {
                const counter = document.createElement('span');
                counter.className = 'files-counter';
                counter.textContent = mobileUploadedFiles.length;
                modalHeader.appendChild(counter);
            }
        }
    }
    
    // 将移动端上传的文件添加到上传列表
    function addMobileFilesToUploadList(mobileFiles) {
        // 将Base64数据URL转换为File对象并添加到文件列表
        if (mobileFiles.length > 0) {
            // 创建一个新的DataTransfer对象
            const dataTransfer = new DataTransfer();
            
            // 如果已经有选择的文件，先添加它们
            if (fileInput.files.length > 0) {
                Array.from(fileInput.files).forEach(file => {
                    dataTransfer.items.add(file);
                });
            }
            
            // 添加移动端上传的文件
            mobileFiles.forEach(fileData => {
                // 从Base64数据URL创建Blob
                const byteString = atob(fileData.dataUrl.split(',')[1]);
                const mimeType = fileData.dataUrl.match(/^data:(.*?);/)[1];
                
                const ab = new ArrayBuffer(byteString.length);
                const ia = new Uint8Array(ab);
                
                for (let i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                
                const blob = new Blob([ab], { type: mimeType });
                const file = new File([blob], fileData.name, { type: mimeType });
                
                // 添加文件到DataTransfer对象
                dataTransfer.items.add(file);
            });
            
            // 更新文件输入
            fileInput.files = dataTransfer.files;
            
            // 更新文件信息
            updateFilesInfo();
            
            // 清空移动端上传的文件列表
            mobileUploadedFiles = [];
        }
    }

    // 显示OCR文字确认弹窗
    window.showConfirmationModal = function(ocrText, taskId) {
        // 创建弹窗HTML
        const modalHtml = `
            <div id="confirmation-modal" class="confirmation-modal">
                <div class="confirmation-modal-content">
                    <div class="confirmation-modal-header">
                        <h3><i class="fas fa-check-circle"></i> 确认文字内容</h3>
                        <span class="confirmation-close-btn">&times;</span>
                    </div>
                    <div class="confirmation-modal-body">
                        <div class="help-text">
                            <p><i class="fas fa-info-circle"></i> 请仔细检查OCR识别的文字内容，如有错误可以直接修改</p>
                        </div>
                        <div class="text-preview-container">
                            <div class="text-preview-header">
                                <h4>OCR识别结果预览</h4>
                                <span class="text-stats">字符数：<strong id="modal-char-count">${ocrText.length}</strong></span>
                            </div>
                            <div class="text-content-preview">${ocrText.replace(/\n/g, '<br>')}</div>
                        </div>
                        <div class="form-group">
                            <label for="modal-confirmed-text">编辑文字内容：</label>
                            <textarea id="modal-confirmed-text" class="form-control" rows="10" placeholder="请在此处编辑文字内容...">${ocrText}</textarea>
                        </div>
                    </div>
                    <div class="confirmation-modal-footer">
                        <button type="button" id="modal-back-btn" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 取消
                        </button>
                        <button type="button" id="modal-reset-btn" class="btn btn-outline">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="button" id="modal-confirm-btn" class="btn btn-primary">
                            <i class="fas fa-check"></i> 确认并继续
                        </button>
                    </div>
                    <div id="modal-loading" class="modal-loading" style="display: none;">
                        <div class="spinner"></div>
                        <p>正在处理，请稍候...</p>
                    </div>
                </div>
            </div>
        `;

        // 添加弹窗到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取弹窗元素
        const modal = document.getElementById('confirmation-modal');
        const textArea = document.getElementById('modal-confirmed-text');
        const charCount = document.getElementById('modal-char-count');
        const closeBtn = modal.querySelector('.confirmation-close-btn');
        const backBtn = document.getElementById('modal-back-btn');
        const resetBtn = document.getElementById('modal-reset-btn');
        const confirmBtn = document.getElementById('modal-confirm-btn');
        const loading = document.getElementById('modal-loading');
        const modalBody = modal.querySelector('.confirmation-modal-body');
        const modalFooter = modal.querySelector('.confirmation-modal-footer');

        // 更新字符计数
        function updateCharCount() {
            charCount.textContent = textArea.value.length;
        }

        // 监听文本变化
        textArea.addEventListener('input', updateCharCount);

        // 关闭弹窗函数
        function closeModal() {
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }

        // 显示加载状态
        function showLoading() {
            modalBody.style.display = 'none';
            modalFooter.style.display = 'none';
            loading.style.display = 'block';
        }

        // 关闭按钮事件
        closeBtn.addEventListener('click', closeModal);

        // 点击弹窗外部关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 取消按钮事件
        backBtn.addEventListener('click', function() {
            if (confirm('确定要取消处理吗？')) {
                showLoading();

                fetch('/api/confirm-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task_id: taskId,
                        action: 'back'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    closeModal();
                    if (data.success) {
                        // 重置进度条和状态
                        progressContainer.style.display = 'none';
                        submitBtn.disabled = false;
                        progressText.textContent = '处理中...';
                        statusStage.textContent = '就绪';
                        progressBar.style.width = '0%';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    closeModal();
                    alert('取消处理失败，请刷新页面');
                });
            }
        });

        // 重置按钮事件
        resetBtn.addEventListener('click', function() {
            if (confirm('确定要重置为原始内容吗？')) {
                textArea.value = ocrText;
                updateCharCount();
            }
        });

        // 确认按钮事件
        confirmBtn.addEventListener('click', function() {
            const confirmedText = textArea.value.trim();
            if (!confirmedText) {
                alert('文字内容不能为空！');
                return;
            }

            showLoading();

            fetch('/api/confirm-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: taskId,
                    action: 'confirm',
                    confirmed_text: confirmedText
                })
            })
            .then(response => response.json())
            .then(data => {
                closeModal();
                if (data.success) {
                    // 继续状态查询循环
                    startStatusCheck();
                } else {
                    alert(data.error || '处理失败，请重试');
                    // 恢复界面
                    progressContainer.style.display = 'none';
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                closeModal();
                alert('网络错误，请重试');
                // 恢复界面
                progressContainer.style.display = 'none';
                submitBtn.disabled = false;
            });
        });

        // 显示弹窗
        modal.style.display = 'flex';
    };

    // 开始状态检查循环
    window.startStatusCheck = function() {
        let lastStatus = '';
        let errorCount = 0;
        const statusCheckInterval = setInterval(function() {
            console.log('查询状态...');
            fetch('/status', {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-Timestamp': new Date().getTime()
                },
                credentials: 'same-origin',
                cache: 'no-store'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`状态查询错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('收到状态数据:', data);
                errorCount = 0;

                if (data.status !== lastStatus) {
                    console.log(`状态变化: ${lastStatus} -> ${data.status}`);
                    lastStatus = data.status;

                    statusStage.classList.add('status-changed');
                    setTimeout(() => {
                        statusStage.classList.remove('status-changed');
                    }, 1000);
                }

                progressText.textContent = `${data.message}`;
                statusStage.textContent = data.status;

                // 根据状态设置进度条
                switch(data.status) {
                    case '开始':
                        progressBar.style.width = '10%';
                        break;
                    case '上传':
                        progressBar.style.width = '30%';
                        break;
                    case 'OCR':
                    case 'OCR中':
                        progressBar.style.width = '50%';
                        break;
                    case 'LLM':
                    case 'LLM处理':
                        progressBar.style.width = '70%';
                        break;
                    case '生成文档':
                        progressBar.style.width = '90%';
                        break;
                    case '文档编辑':
                        progressBar.style.width = '85%';
                        clearInterval(statusCheckInterval);
                        if (data.edit_url) {
                            setTimeout(() => {
                                window.location.href = data.edit_url;
                            }, 500);
                        }
                        break;
                    case '完成':
                        progressBar.style.width = '100%';
                        clearInterval(statusCheckInterval);
                        setTimeout(() => {
                            window.location.href = '/result';
                        }, 1000);
                        break;
                }
            })
            .catch(error => {
                console.error('状态查询失败:', error);
                errorCount++;
                if (errorCount >= 5) {
                    clearInterval(statusCheckInterval);
                    alert('状态查询失败，请刷新页面重试');
                }
            });
        }, 2000);
    };
});
