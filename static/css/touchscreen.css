/* static/css/touchscreen.css */
/* 本文件定义触摸屏界面的样式 */

html {
    /* 禁止文本选择，防止长按触发上下文菜单 */
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none;   /* Safari */
    -khtml-user-select: none;    /* Konqueror HTML */
    -moz-user-select: none;      /* Firefox */
    -ms-user-select: none;       /* Internet Explorer/Edge */
    user-select: none;           /* 非前缀版本，目前支持Chrome和Opera */
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
    background-color: #f0f2f5;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    -webkit-tap-highlight-color: transparent; /* 移除触摸高亮效果 */
    /* 禁止长按操作 */
    -webkit-touch-callout: none;
}

.container {
    flex: 1;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin: 30px 30px 30px 230px; /* 为固定侧边导航留出空间 */
}

/* 头部样式 */
header {
    background-color: #4a90e2; /* Kiosk 风格蓝色 */
    color: #fff;
    padding: 30px 40px;
    text-align: center;
}

header h1 {
    font-size: 2.5em;
    margin: 0 0 10px 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-logo {
    height: 40px;
    margin-right: 15px;
    vertical-align: middle;
}

header p {
    font-size: 1.2em;
    margin: 0;
    opacity: 0.9;
}

/* 主内容区域 */
main {
    padding: 40px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 30px; /* 模块间距 */
    overflow: hidden; /* 防止内容溢出 */
}

/* 文件上传区域（已修改为拍照区域） */
.upload-container {
    border: 2px dashed #4a90e2;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    background-color: #f9faff;
    transition: background-color 0.3s ease;
    margin-bottom: 0; /* 确保底部没有额外的外边距 */
}

/* 相机区域样式 */
.camera-area {
    position: relative;
    width: 100%;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto; /* 确保水平居中 */
}

.camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.camera-placeholder .drop-icon {
    font-size: 4em;
    color: #4a90e2;
    margin-bottom: 20px;
}

.camera-placeholder p {
    font-size: 1.2em;
    color: #555;
    margin: 10px 0;
}

.camera-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin: 20px 0;
}

.camera-btn {
    display: inline-block;
    background-color: #4a90e2;
    color: #fff;
    padding: 16px 30px;
    border-radius: 10px;
    font-size: 1.3em;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    border: none;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.camera-btn i {
    font-size: 1.4em;
}

.camera-btn:hover {
    background-color: #3a7bc8;
    transform: translateY(-3px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.camera-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.qr-btn {
    background-color: #9b59b6;
}

.qr-btn:hover {
    background-color: #8e44ad;
    transform: translateY(-3px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.qr-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

/* 摄像头视图 */
.camera-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row; /* 改为水平布局 */
    align-items: center;
    justify-content: space-between; /* 在水平方向上分散元素 */
    padding: 20px; /* 添加四周内边距 */
    position: relative; /* 添加相对定位 */
}

#video {
    width: 75%; /* 增加宽度，从60%增加到75% */
    max-width: 500px; /* 增加最大宽度，从400px增加到500px */
    max-height: 375px; /* 增加最大高度，从300px增加到375px */
    background-color: #000;
    border-radius: 8px;
    margin-right: 20px; /* 添加右边距，与按钮分开 */
}

.camera-controls {
    display: flex;
    flex-direction: column; /* 改为垂直布局 */
    gap: 15px;
    width: 20%; /* 减小控制按钮区域宽度，从35%减小到20% */
    justify-content: center;
    z-index: 10; /* 确保控制按钮在视频上方 */
}

.capture-btn {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1.1em;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
}

.capture-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1.1em;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
}

.cancel-btn:hover {
    background-color: #c0392b;
}

/* 照片预览 */
.photo-preview {
    width: 100%;
    display: flex;
    flex-direction: row; /* 改为水平布局 */
    align-items: center;
    justify-content: space-between; /* 在水平方向上分散元素 */
    padding: 20px; /* 添加四周内边距 */
}

#canvas {
    width: 75%; /* 增加宽度，从60%增加到75% */
    max-width: 500px; /* 增加最大宽度，从400px增加到500px */
    max-height: 375px; /* 增加最大高度，从300px增加到375px */
    background-color: #f0f0f0;
    border-radius: 8px;
    margin-right: 20px; /* 添加右边距，与按钮分开 */
    object-fit: contain; /* 确保图像保持比例 */
}

.preview-controls {
    display: flex;
    flex-direction: column; /* 改为垂直布局 */
    gap: 15px;
    width: 20%; /* 减小控制按钮区域宽度，从35%减小到20% */
}

.save-btn {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1.1em;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
}

.save-btn:hover {
    background-color: #27ae60;
}

.retake-btn {
    background-color: #f39c12;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1.1em;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
}

.retake-btn:hover {
    background-color: #d35400;
}

/* 添加更多照片的按钮 */
.add-more-btn {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    margin-top: 15px;
    font-size: 1em;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    width: 100%;
    transition: background-color 0.3s ease;
}

.add-more-btn:hover {
    background-color: #3a7bc8;
}

.add-more-btn i {
    font-size: 1.2em;
}

/* 文件列表 */
.file-list-container {
    margin: 30px 0; /* 修改为上下外边距一致 */
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    background-color: #fff;
}

.file-list-container h3 {
    font-size: 1.3em;
    color: #333;
    margin: 0 0 15px 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.file-list-container h3 i {
    margin-right: 10px;
    color: #4a90e2;
}

.file-list-container small {
    font-size: 0.8em;
    color: #777;
    font-weight: normal;
}

/* 照片列表样式 */
.sortable-file-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.sortable-file-list .file-item {
    display: flex;
    flex-direction: column;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    cursor: grab;
    height: 180px;
}

.sortable-file-list .file-preview {
    width: 100%;
    height: 150px;
    overflow: hidden;
    position: relative;
    background-color: #eee;
}

.sortable-file-list .file-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.sortable-file-list .file-info {
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    border-top: 1px solid #e0e0e0;
}

.sortable-file-list .file-name {
    font-size: 0.85em;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 85px;
}

.sortable-file-list .remove-file-btn {
    background: none;
    border: none;
    color: #e74c3c;
    font-size: 1.2em;
    cursor: pointer;
    padding: 2px;
    transition: color 0.2s;
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.sortable-file-list .remove-file-btn:hover {
    color: #c0392b;
    background-color: rgba(255, 255, 255, 0.9);
}

/* Sortable样式 */
.sortable-ghost {
    opacity: 0.5;
    background-color: #e0e0e0;
}

.sortable-chosen {
    box-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
}

.sortable-drag {
    opacity: 0.9;
}

/* 选项区域 - 隐藏分类选择 */
.options {
    display: none; /* 隐藏整个选项区域 */
}

/* 选项区域的原始样式，保留以便未来需要时重新启用 */
.options-visible {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 25px; /* 选项组间距 */
    margin-top: 10px;
    background-color: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.option-group {
    display: flex;
    align-items: center;
    flex-basis: calc(50% - 12.5px); /* 每行两个，考虑间距 */
    gap: 10px;
}

.option-group label {
    font-size: 1.1em;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.option-group label i {
    margin-right: 8px;
    color: #4a90e2;
    width: 20px; /* 固定图标宽度 */
    text-align: center;
}

.option-group select {
    padding: 10px 15px;
    font-size: 1em;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    flex-grow: 1; /* 填充剩余空间 */
    cursor: pointer;
    appearance: none; /* 移除默认下拉箭头 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234a90e2'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
    padding-right: 40px; /* 为自定义箭头留出空间 */
    margin-bottom: 10px;
}

/* 四级选择的特殊样式 */
#manual-template-container select {
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

#manual-template-container select:last-child {
    margin-bottom: 0;
}

/* 模板选择容器样式 */
.template-selection-container {
    background: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 2px solid #4a90e2;
}

.template-selection-container h2 {
    color: #4a90e2;
    margin-bottom: 10px;
    font-size: 1.4em;
    text-align: center;
}

.template-selection-container p {
    color: #666;
    margin-bottom: 30px;
    font-size: 1em;
    text-align: center;
}

/* 左右布局 */
.template-layout {
    display: flex;
    gap: 30px;
    min-height: 500px;
}

/* 常用模板区域（右侧） */
.common-templates-section {
    flex: 1;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #28a745;
}

.common-templates-section h3 {
    color: #28a745;
    margin-bottom: 20px;
    font-size: 1.2em;
    text-align: center;
}

/* 完整模板区域（左侧） */
.full-templates-section {
    flex: 1.5;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #4a90e2;
}

.full-templates-section h3 {
    color: #4a90e2;
    margin-bottom: 20px;
    font-size: 1.2em;
    text-align: center;
}

/* 模板卡片网格 */
.template-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

/* 模板卡片样式 */
.template-card {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    border-color: #4a90e2;
}

.template-card.selected {
    border-color: #28a745;
    background: #d4edda;
}

.template-card.common-template:hover {
    border-color: #28a745;
}

.template-card .card-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
    color: #4a90e2;
}

.template-card.common-template .card-icon {
    color: #28a745;
}

.template-card .card-content h4 {
    margin: 0 0 8px 0;
    font-size: 1.1em;
    color: #333;
    font-weight: bold;
}

.template-card .card-content p {
    margin: 0;
    font-size: 0.9em;
    color: #666;
    line-height: 1.3;
}

/* 子分类标题 */
.subcategory-title {
    grid-column: 1 / -1;
    margin: 20px 0 10px 0;
    padding: 10px 15px;
    background: #e9ecef;
    border-radius: 6px;
    border-left: 4px solid #4a90e2;
}

.subcategory-title h4 {
    margin: 0;
    color: #4a90e2;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.subcategory-title h4 i {
    font-size: 1em;
}

/* 批次选择器 */
.batch-selector {
    margin-bottom: 20px;
}

.batch-tabs {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.batch-tab {
    flex: 1;
    padding: 12px 20px;
    border: 2px solid #ddd;
    background: #fff;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1em;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.batch-tab:hover {
    border-color: #4a90e2;
    background: #f8f9fa;
}

.batch-tab.active {
    border-color: #4a90e2;
    background: #4a90e2;
    color: #fff;
}

.batch-tab i {
    font-size: 1.1em;
}

/* 分类容器 */
.categories-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
}

/* 分类卡片样式 */
.category-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #4a90e2;
}

.category-card .category-icon {
    font-size: 2em;
    margin-bottom: 10px;
    color: #4a90e2;
}

.category-card .category-name {
    font-size: 1em;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.category-card .category-count {
    font-size: 0.8em;
    color: #666;
    margin-top: 5px;
}

/* 返回按钮 */
.back-button {
    margin-bottom: 20px;
}

.btn-back {
    padding: 10px 20px;
    background: #6c757d;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
}

.btn-back:hover {
    background: #5a6268;
}

.btn-back i {
    font-size: 1em;
}

/* 模板确认区域 */
.template-confirm {
    background: #d4edda;
    border: 2px solid #c3e6cb;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.selected-template-info {
    display: flex;
    align-items: center;
    color: #155724;
    font-size: 1.1em;
    margin-bottom: 20px;
    justify-content: center;
}

.selected-template-info i {
    color: #28a745;
    margin-right: 10px;
    font-size: 1.2em;
}

/* 模板操作按钮 */
.template-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    background: #fff;
    border-radius: 10px;
    padding: 25px 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 140px;
    min-width: 160px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 1.1em;
    font-weight: bold;
    color: #333;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.action-btn i {
    font-size: 2.5em;
    margin-bottom: 10px;
    display: block;
}

.print-btn {
    border-color: #6c757d;
}

.print-btn i {
    color: #6c757d;
}

.print-btn:hover {
    border-color: #6c757d;
    background: #f8f9fa;
}

.upload-btn {
    border-color: #28a745;
}

.upload-btn i {
    color: #28a745;
}

.upload-btn:hover {
    border-color: #28a745;
    background: #f8f9fa;
}

.edit-btn {
    border-color: #007bff;
}

.edit-btn i {
    color: #007bff;
}

.edit-btn:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

/* 上传容器调整 */
.upload-container h2 {
    color: #28a745;
    margin-bottom: 10px;
    font-size: 1.4em;
}

.upload-container p {
    color: #666;
    margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .template-layout {
        flex-direction: column;
        gap: 20px;
    }

    .template-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .template-card {
        padding: 15px;
        min-height: 120px;
    }

    .template-card .card-icon {
        font-size: 2em;
    }

    .template-card .card-content h4 {
        font-size: 1em;
    }

    .categories-container {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .category-card {
        padding: 15px;
        min-height: 100px;
    }

    .batch-tabs {
        flex-direction: column;
    }

    .batch-tab {
        padding: 10px 15px;
    }

    /* 模板操作按钮响应式 */
    .template-actions {
        flex-direction: column;
        gap: 10px;
    }

    .action-btn {
        max-width: none;
        padding: 12px 15px;
        font-size: 1em;
    }
}

/* 提交按钮 */
.submit-btn {
    display: block; /* 独占一行 */
    width: 100%; /* 宽度100% */
    padding: 18px 30px;
    font-size: 1.4em;
    font-weight: 600;
    color: #fff;
    background-color: #2ecc71; /* Kiosk 风格绿色 */
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.1s ease;
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.submit-btn:hover {
    background-color: #27ae60;
}

.submit-btn:active {
    transform: scale(0.98); /* 点击反馈 */
}

.submit-btn:disabled {
    background-color: #a0c9a9;
    cursor: not-allowed;
}

/* 模板下载区域 */
.template-download-container {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 30px; /* 添加底部外边距，保持与其他元素一致 */
}

.template-download-container h3 {
    font-size: 1.5em;
    color: #333;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.template-download-container h3 i {
    color: #4a90e2;
}

.template-download-container p {
    font-size: 1.1em;
    color: #555;
    margin-bottom: 25px;
}

.template-select-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

.template-select {
    flex-grow: 1;
    padding: 12px 15px;
    font-size: 1em;
    border: 1px solid #ccc;
    border-radius: 5px;
    cursor: pointer;
    appearance: none; /* 移除默认下拉箭头 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234a90e2'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
    padding-right: 40px; /* 为自定义箭头留出空间 */
}

.download-template-btn {
    padding: 12px 25px;
    font-size: 1em;
    color: #fff;
    background-color: #4a90e2;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-template-btn:hover:not(:disabled) {
    background-color: #3a7bc8;
}

.download-template-btn:disabled {
    background-color: #a0c4e9;
    cursor: not-allowed;
}

/* 进度条 */
.progress-container {
    margin: 30px 0; /* 修改为上下外边距一致 */
    padding: 25px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    text-align: center;
}

/* 结果页面按钮样式 */
.download-container, .error-restart-container {
    margin-top: 20px;
    text-align: center;
}

/* 倒计时容器样式 */
.countdown-container {
    margin: 15px 0;
    padding: 10px;
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 5px;
    text-align: center;
    font-size: 1.2em;
    color: #856404;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.countdown-container i {
    color: #e0a800;
}

#countdown-timer {
    font-weight: bold;
    font-size: 1.3em;
}

/* 结果页面按钮样式 */
.download-container .download-btn,
.download-container .print-btn,
.error-restart-container .restart-btn {
    display: inline-block;
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    font-size: 1.2em;
    cursor: pointer;
    color: #fff;
    margin: 0 5px;
    transition: background-color 0.3s ease;
}

.download-container .download-btn {
    background-color: #4a90e2;
}

.download-container .download-btn:hover {
    background-color: #3a7bc8;
}

.download-container .print-btn {
    background-color: #3498db;
}

.download-container .print-btn:hover {
    background-color: #2980b9;
}

.error-restart-container .restart-btn {
    background-color: #27ae60;
}

.error-restart-container .restart-btn:hover {
    background-color: #219955;
}

.progress-bar {
    width: 100%;
    height: 25px;
    background-color: #e9ecef;
    border-radius: 12.5px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress {
    width: 0%;
    height: 100%;
    background-color: #4a90e2;
    transition: width 0.5s ease;
    border-radius: 12.5px;
}

#progress-text {
    font-size: 1.1em;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.status-info {
    font-size: 1em;
    color: #555;
}

.status-info i {
    margin-right: 5px;
    color: #4a90e2;
}

/* 二维码模态框样式 */
.modal, .qr-code-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

.modal-content, .qr-code-modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: slideIn 0.3s;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header, .qr-code-modal-header {
    padding: 20px;
    background-color: #4a90e2;
    color: white;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3, .qr-code-modal-header h3 {
    margin: 0;
    font-size: 1.5em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn, .qr-code-modal-close {
    color: white;
    font-size: 1.8em;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close-btn:hover, .qr-code-modal-close:hover {
    color: #f1f1f1;
}

.modal-scroll-container {
    overflow-y: auto;
    max-height: calc(90vh - 70px); /* 减去header高度 */
}

.modal-body {
    padding: 20px;
}

.qr-code-container {
    text-align: center;
    margin: 20px 0;
}

.qr-code-container img {
    max-width: 250px;
    height: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
}

.qr-code-instructions {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #4a90e2;
}

.qr-code-instructions p {
    margin: 10px 0;
    font-size: 1.1em;
    color: #333;
}

.session-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #f0f7ff;
    border-radius: 5px;
    border: 1px solid #d0e3ff;
}

.session-info p {
    margin: 5px 0;
    font-size: 1em;
    color: #333;
}

#connection-status {
    font-weight: bold;
    color: #4a90e2;
}

.mobile-files-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
}

.mobile-files-container h4 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.mobile-files-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.mobile-file-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.mobile-file-preview {
    width: 100%;
    height: 120px;
    overflow: hidden;
    position: relative;
    background-color: #eee;
}

.mobile-file-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.mobile-file-info {
    padding: 8px;
    font-size: 0.85em;
    color: #333;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@keyframes fadeIn {
    from {opacity: 0}
    to {opacity: 1}
}

@keyframes slideIn {
    from {transform: translateY(-50px); opacity: 0;}
    to {transform: translateY(0); opacity: 1;}
}

/* 自定义通知弹窗 */
.notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 0;
    width: 90%;
    max-width: 400px;
    z-index: 2000;
    animation: fadeIn 0.3s;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.notification-header {
    padding: 15px 20px;
    background-color: #4a90e2;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px 10px 0 0;
}

.notification-header h3 {
    margin: 0;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-body {
    padding: 20px;
    text-align: center;
    font-size: 1.1em;
    color: #333;
}

.notification-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #eee;
}

.notification-btn {
    padding: 10px 20px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s;
}

.notification-btn:hover {
    background-color: #3a7bc8;
}

.notification-btn.download-btn {
    background-color: #28a745;
    margin-right: 10px;
}

.notification-btn.download-btn:hover {
    background-color: #218838;
}

.notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1999;
    animation: fadeIn 0.3s;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        width: 95%;
        border-radius: 10px;
    }

    header {
        padding: 25px 30px;
    }

    header h1 {
        font-size: 2em;
    }

    header p {
        font-size: 1em;
    }

    main {
        padding: 30px;
    }

    .upload-container {
        padding: 30px;
    }

    /* 在移动设备上恢复垂直布局 */
    .camera-view, .photo-preview {
        flex-direction: column;
        padding: 15px;
    }

    #video, #canvas {
        width: 100%;
        max-width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }

    .camera-controls, .preview-controls {
        width: 100%;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .capture-btn, .cancel-btn, .save-btn, .retake-btn {
        width: 100%;
    }

    .file-input-label {
        padding: 10px 20px;
        font-size: 1em;
    }

    .options {
        padding: 20px;
    }

    .option-group {
        flex-basis: 100%; /* 在小屏幕上每行一个 */
    }

    .submit-btn {
        padding: 15px 25px;
        font-size: 1.2em;
    }

    .template-download-container {
        padding: 25px;
    }

    .template-select-group {
        flex-direction: column; /* 垂直堆叠 */
        align-items: stretch; /* 拉伸元素以填充宽度 */
    }

    .template-select {
        margin-bottom: 10px; /* 添加间距 */
    }

    .download-template-btn {
        width: 100%; /* 宽度100% */
    }
}

@media (max-width: 480px) {
    header {
        padding: 20px;
    }

    header h1 {
        font-size: 1.8em;
    }

    main {
        padding: 20px;
    }

    .upload-container {
        padding: 25px;
    }

    .sortable-file-list .file-item {
        padding: 10px;
    }

    .sortable-file-list .file-icon {
        font-size: 1.3em;
        margin-right: 10px;
    }

    .sortable-file-list .file-name {
        font-size: 1em;
    }

    .option-group label {
        font-size: 1em;
    }

    .option-group select {
        padding: 8px 12px;
        font-size: 0.9em;
    }

    .submit-btn {
        padding: 12px 20px;
        font-size: 1.1em;
    }

    .template-download-container h3 {
        font-size: 1.3em;
    }

    .template-download-container p {
        font-size: 1em;
    }

    .template-select,
    .download-template-btn {
        font-size: 0.9em;
        padding: 10px 15px;
    }

    .camera-controls button, .preview-controls button {
        padding: 10px 15px;
        font-size: 1em;
    }
}

/* 侧边导航样式已移至共享文件 static/css/shared-navigation.css */

/* 响应式设计已移至共享文件 static/css/shared-navigation.css */